<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service - HTML Pro Tools</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/legal.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-code"></i>
                    <span>HTML Pro Tools</span>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link">About</a>
                    <a href="privacy.html" class="nav-link">Privacy</a>
                    <a href="terms.html" class="nav-link active">Terms</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main">
        <section class="page-hero">
            <div class="container">
                <h1 class="page-title">Terms of Service</h1>
                <p class="page-subtitle">Please read these terms carefully before using our service</p>
                <div class="last-updated">Last updated: January 2024</div>
            </div>
        </section>

        <section class="legal-content">
            <div class="container">
                <div class="content-wrapper">
                    <div class="toc">
                        <h3>Table of Contents</h3>
                        <ul>
                            <li><a href="#acceptance">Acceptance of Terms</a></li>
                            <li><a href="#description">Service Description</a></li>
                            <li><a href="#usage">Acceptable Use</a></li>
                            <li><a href="#intellectual">Intellectual Property</a></li>
                            <li><a href="#disclaimer">Disclaimer</a></li>
                            <li><a href="#limitation">Limitation of Liability</a></li>
                            <li><a href="#availability">Service Availability</a></li>
                            <li><a href="#modifications">Modifications</a></li>
                            <li><a href="#termination">Termination</a></li>
                            <li><a href="#governing">Governing Law</a></li>
                            <li><a href="#contact">Contact Information</a></li>
                        </ul>
                    </div>

                    <div class="legal-document">
                        <section id="acceptance" class="legal-section">
                            <h2><i class="fas fa-handshake"></i> Acceptance of Terms</h2>
                            <p>By accessing and using HTML Pro Tools, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.</p>
                            <div class="highlight-box">
                                <i class="fas fa-info-circle"></i>
                                <p><strong>Important:</strong> These terms constitute a legally binding agreement between you and HTML Pro Tools.</p>
                            </div>
                        </section>

                        <section id="description" class="legal-section">
                            <h2><i class="fas fa-tools"></i> Service Description</h2>
                            <p>HTML Pro Tools provides web-based HTML processing tools including:</p>
                            <ul>
                                <li>HTML code beautification and formatting</li>
                                <li>HTML compression and minification</li>
                                <li>External link removal and cleaning</li>
                                <li>CSS inlining capabilities</li>
                                <li>JavaScript variable conversion</li>
                                <li>Comment removal functionality</li>
                            </ul>
                            <p>All processing is performed client-side in your browser without transmitting your code to our servers.</p>
                        </section>

                        <section id="usage" class="legal-section">
                            <h2><i class="fas fa-check-circle"></i> Acceptable Use</h2>
                            <h3>You May:</h3>
                            <ul>
                                <li>Use our tools for personal and commercial projects</li>
                                <li>Process HTML code of any size within browser limitations</li>
                                <li>Share links to our service with others</li>
                                <li>Provide feedback and suggestions for improvement</li>
                            </ul>
                            
                            <h3>You May Not:</h3>
                            <ul>
                                <li>Use the service for illegal activities</li>
                                <li>Attempt to reverse engineer or copy our tools</li>
                                <li>Overload our servers with excessive requests</li>
                                <li>Use the service to process malicious code</li>
                                <li>Redistribute or resell our service</li>
                                <li>Remove or modify copyright notices</li>
                            </ul>
                        </section>

                        <section id="intellectual" class="legal-section">
                            <h2><i class="fas fa-copyright"></i> Intellectual Property Rights</h2>
                            <p>The service and its original content, features, and functionality are and will remain the exclusive property of HTML Pro Tools and its licensors.</p>
                            <div class="highlight-box">
                                <i class="fas fa-shield-alt"></i>
                                <p><strong>Your Content:</strong> You retain all rights to the HTML code you process. We claim no ownership over your content.</p>
                            </div>
                            <p>The service is protected by copyright, trademark, and other laws.</p>
                        </section>

                        <section id="disclaimer" class="legal-section">
                            <h2><i class="fas fa-exclamation-triangle"></i> Disclaimer of Warranties</h2>
                            <p>The information on this website is provided on an "as is" basis. To the fullest extent permitted by law, this Company:</p>
                            <ul>
                                <li>Excludes all representations and warranties relating to this website and its contents</li>
                                <li>Does not guarantee the accuracy, completeness, or reliability of the tools</li>
                                <li>Makes no warranties about the availability or functionality of the service</li>
                                <li>Does not warrant that the service will be error-free or uninterrupted</li>
                            </ul>
                        </section>

                        <section id="limitation" class="legal-section">
                            <h2><i class="fas fa-balance-scale"></i> Limitation of Liability</h2>
                            <p>HTML Pro Tools shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation:</p>
                            <ul>
                                <li>Loss of profits, data, use, goodwill, or other intangible losses</li>
                                <li>Damages resulting from your use or inability to use the service</li>
                                <li>Any conduct or content of any third party on the service</li>
                                <li>Any content obtained from the service</li>
                            </ul>
                            <div class="highlight-box warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p><strong>Important:</strong> Always backup your code before processing. We are not responsible for any data loss.</p>
                            </div>
                        </section>

                        <section id="availability" class="legal-section">
                            <h2><i class="fas fa-server"></i> Service Availability</h2>
                            <p>We strive to maintain high availability but cannot guarantee uninterrupted service. The service may be temporarily unavailable due to:</p>
                            <ul>
                                <li>Scheduled maintenance</li>
                                <li>Technical difficulties</li>
                                <li>Internet connectivity issues</li>
                                <li>Force majeure events</li>
                            </ul>
                        </section>

                        <section id="modifications" class="legal-section">
                            <h2><i class="fas fa-edit"></i> Modifications to Terms</h2>
                            <p>We reserve the right to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.</p>
                            <p>What constitutes a material change will be determined at our sole discretion.</p>
                        </section>

                        <section id="termination" class="legal-section">
                            <h2><i class="fas fa-times-circle"></i> Termination</h2>
                            <p>We may terminate or suspend access immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.</p>
                            <p>Upon termination, your right to use the service will cease immediately.</p>
                        </section>

                        <section id="governing" class="legal-section">
                            <h2><i class="fas fa-gavel"></i> Governing Law</h2>
                            <p>These Terms shall be interpreted and governed by the laws of the jurisdiction in which HTML Pro Tools operates, without regard to its conflict of law provisions.</p>
                            <p>Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights.</p>
                        </section>

                        <section id="contact" class="legal-section">
                            <h2><i class="fas fa-envelope"></i> Contact Information</h2>
                            <p>If you have any questions about these Terms of Service, please contact us:</p>
                            <div class="contact-info">
                                <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                                <p><strong>Subject Line:</strong> Terms of Service Inquiry</p>
                            </div>
                        </section>

                        <section class="legal-section">
                            <h2><i class="fas fa-calendar-alt"></i> Effective Date</h2>
                            <p>These Terms of Service are effective as of January 2024 and will remain in effect except with respect to any changes in its provisions in the future, which will be in effect immediately after being posted on this page.</p>
                        </section>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>HTML Pro Tools</h4>
                    <p>Professional HTML processing made simple</p>
                </div>
                <div class="footer-section">
                    <h4>Popular Apps</h4>
                    <div class="app-icons">
                        <a href="https://github.com" target="_blank" title="GitHub">
                            <i class="fab fa-github"></i>
                            <span>GitHub</span>
                        </a>
                        <a href="https://codepen.io" target="_blank" title="CodePen">
                            <i class="fab fa-codepen"></i>
                            <span>CodePen</span>
                        </a>
                        <a href="https://stackoverflow.com" target="_blank" title="Stack Overflow">
                            <i class="fab fa-stack-overflow"></i>
                            <span>Stack Overflow</span>
                        </a>
                        <a href="https://developer.mozilla.org" target="_blank" title="MDN Web Docs">
                            <i class="fab fa-firefox-browser"></i>
                            <span>MDN Docs</span>
                        </a>
                        <a href="https://code.visualstudio.com" target="_blank" title="VS Code">
                            <i class="fas fa-code"></i>
                            <span>VS Code</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');

        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });

        // Smooth scrolling for TOC links
        document.querySelectorAll('.toc a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
