<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Pro Tools - Advanced HTML Processing Suite</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-code"></i>
                    <span>HTML Pro Tools</span>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <a href="index.html" class="nav-link active">Home</a>
                    <a href="about.html" class="nav-link">About</a>
                    <a href="privacy.html" class="nav-link">Privacy</a>
                    <a href="terms.html" class="nav-link">Terms</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main">
        <section class="hero">
            <div class="container">
                <h1 class="hero-title">Advanced HTML Processing Suite</h1>
                <p class="hero-subtitle">Beautify, compress, clean, and transform your HTML code with professional-grade tools</p>
            </div>
        </section>

        <section class="tools-section">
            <div class="container">
                <div class="tools-grid">
                    <!-- HTML Input Area -->
                    <div class="tool-card input-card">
                        <div class="card-header">
                            <h3><i class="fas fa-file-code"></i> HTML Input</h3>
                            <div class="input-actions">
                                <button class="btn btn-secondary" id="clearInput">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                                <button class="btn btn-secondary" id="loadSample">
                                    <i class="fas fa-download"></i> Load Sample
                                </button>
                            </div>
                        </div>
                        <textarea id="htmlInput" placeholder="Paste your HTML code here..."></textarea>
                    </div>

                    <!-- Processing Options -->
                    <div class="tool-card options-card">
                        <div class="card-header">
                            <h3><i class="fas fa-cogs"></i> Processing Options</h3>
                        </div>
                        <div class="options-grid">
                            <div class="option-group">
                                <h4>Formatting</h4>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="beautify" checked>
                                    <span class="checkmark"></span>
                                    Beautify HTML
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="compress">
                                    <span class="checkmark"></span>
                                    Compress HTML
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="inlineCSS">
                                    <span class="checkmark"></span>
                                    Inline CSS
                                </label>
                            </div>
                            <div class="option-group">
                                <h4>Cleaning</h4>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="removeLinks">
                                    <span class="checkmark"></span>
                                    Remove External Links
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="removeComments">
                                    <span class="checkmark"></span>
                                    Remove Comments
                                </label>
                            </div>
                            <div class="option-group">
                                <h4>Conversion</h4>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="toJSVariable">
                                    <span class="checkmark"></span>
                                    Convert to JS Variable
                                </label>
                                <div class="js-var-options" id="jsVarOptions" style="display: none;">
                                    <input type="text" id="varName" placeholder="Variable name" value="htmlContent">
                                    <select id="varType">
                                        <option value="const">const</option>
                                        <option value="let">let</option>
                                        <option value="var">var</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary process-btn" id="processHTML">
                            <i class="fas fa-magic"></i> Process HTML
                        </button>
                    </div>

                    <!-- Output Area -->
                    <div class="tool-card output-card">
                        <div class="card-header">
                            <h3><i class="fas fa-file-export"></i> Processed Output</h3>
                            <div class="output-actions">
                                <button class="btn btn-secondary" id="copyOutput">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                                <button class="btn btn-secondary" id="downloadOutput">
                                    <i class="fas fa-download"></i> Download
                                </button>
                            </div>
                        </div>
                        <textarea id="htmlOutput" readonly placeholder="Processed HTML will appear here..."></textarea>
                        <div class="output-stats" id="outputStats"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>HTML Pro Tools</h4>
                    <p>Professional HTML processing made simple</p>
                </div>
                <div class="footer-section">
                    <h4>Popular Apps</h4>
                    <div class="app-icons">
                        <a href="https://github.com" target="_blank" title="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://codepen.io" target="_blank" title="CodePen">
                            <i class="fab fa-codepen"></i>
                        </a>
                        <a href="https://stackoverflow.com" target="_blank" title="Stack Overflow">
                            <i class="fab fa-stack-overflow"></i>
                        </a>
                        <a href="https://developer.mozilla.org" target="_blank" title="MDN Web Docs">
                            <i class="fab fa-firefox-browser"></i>
                        </a>
                        <a href="https://code.visualstudio.com" target="_blank" title="VS Code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
