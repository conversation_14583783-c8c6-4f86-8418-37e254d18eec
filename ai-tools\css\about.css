/* About Page Enhanced Styles */

.page-hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.page-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.page-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.about-content {
    padding: 6rem 0;
    background: var(--bg-primary);
    position: relative;
}

.about-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 40%);
    pointer-events: none;
}

.about-hero {
    text-align: center;
    margin-bottom: 6rem;
    position: relative;
    z-index: 1;
}

.about-hero h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-hero p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.8;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 6rem;
    position: relative;
    z-index: 1;
}

.content-card {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 24px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 24px 24px 0 0;
}

.content-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.content-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.2);
    border-color: var(--primary-color);
}

.content-card:hover::after {
    opacity: 1;
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 1;
}

.content-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.content-card p {
    color: var(--text-secondary);
    line-height: 1.7;
    position: relative;
    z-index: 1;
}

.feature-list {
    list-style: none;
    text-align: left;
    margin-top: 1.5rem;
    position: relative;
    z-index: 1;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-list li:hover {
    background: rgba(255, 255, 255, 0.05);
}

.feature-list i {
    color: var(--success-color);
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.stats-section {
    background: linear-gradient(145deg, var(--bg-secondary), var(--bg-tertiary));
    border-radius: 24px;
    padding: 4rem 2rem;
    margin-bottom: 6rem;
    text-align: center;
    border: 1px solid var(--border-light);
    position: relative;
    z-index: 1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.developer-info {
    margin-bottom: 6rem;
    position: relative;
    z-index: 1;
}

.info-card {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 24px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 24px 24px 0 0;
}

.info-card h2 {
    color: var(--text-primary);
    margin-bottom: 3rem;
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.info-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.info-section {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.info-section:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.info-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.125rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-section h4 i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.25rem;
}

.info-section p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.7;
}

.contact-email {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    padding: 1rem 1.5rem;
    background: var(--gradient-primary);
    border-radius: 12px;
    transition: all 0.3s ease;
    margin-top: 1rem;
    box-shadow: var(--shadow-md);
}

.contact-email:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.technology-stack {
    margin-bottom: 6rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.technology-stack h2 {
    color: var(--text-primary);
    margin-bottom: 3rem;
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 20px;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.tech-item:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-xl), 0 0 30px rgba(99, 102, 241, 0.2);
}

.tech-item i {
    font-size: 2.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tech-item span {
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
}

.version-info {
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.version-card {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 24px;
    padding: 3rem;
    max-width: 500px;
    width: 100%;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.version-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 24px 24px 0 0;
}

.version-card h3 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.version-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.version-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.version-label {
    color: var(--text-secondary);
    font-weight: 600;
}

.version-value {
    color: var(--text-primary);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }

    .about-hero h2 {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .info-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .content-card,
    .info-card,
    .version-card {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .about-hero h2 {
        font-size: 1.75rem;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .version-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .content-card,
    .info-card,
    .version-card {
        padding: 1.5rem;
    }
}

/* Enhanced Animations */
.content-card {
    animation: fadeInUp 0.8s ease-out;
}

.content-card:nth-child(2) {
    animation-delay: 0.2s;
}

.content-card:nth-child(3) {
    animation-delay: 0.4s;
}

.content-card:nth-child(4) {
    animation-delay: 0.6s;
}

.info-card {
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.technology-stack {
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.version-info {
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.stats-section {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}
