<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - HTML Pro Tools</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/about.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-code"></i>
                    <span>HTML Pro Tools</span>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link active">About</a>
                    <a href="privacy.html" class="nav-link">Privacy</a>
                    <a href="terms.html" class="nav-link">Terms</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main">
        <section class="page-hero">
            <div class="container">
                <h1 class="page-title">About HTML Pro Tools</h1>
                <p class="page-subtitle">Professional HTML processing made simple and accessible</p>
            </div>
        </section>

        <section class="about-content">
            <div class="container">
                <div class="about-hero">
                    <h2>Empowering Developers Worldwide</h2>
                    <p>HTML Pro Tools is a comprehensive suite of client-side HTML processing utilities designed to streamline your development workflow. Built with modern web technologies and a focus on privacy, our tools help you format, optimize, and transform HTML code efficiently.</p>
                </div>

                <div class="stats-section">
                    <h2>Trusted by Developers</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Client-Side</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">6+</span>
                            <span class="stat-label">Core Features</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Data Stored</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">∞</span>
                            <span class="stat-label">Usage Limit</span>
                        </div>
                    </div>
                </div>

                <div class="content-grid">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h3>Smart Beautification</h3>
                        <p>Transform messy HTML into clean, properly indented code with our intelligent formatting engine. Supports nested structures and preserves content integrity.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-compress-arrows-alt"></i>
                        </div>
                        <h3>Advanced Compression</h3>
                        <p>Reduce file sizes significantly with our smart compression algorithm that removes unnecessary whitespace while maintaining functionality.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h3>CSS Inlining</h3>
                        <p>Convert external CSS styles to inline styles automatically, perfect for email templates and standalone HTML documents.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>JS Variable Export</h3>
                        <p>Convert HTML content into JavaScript variables with proper escaping, ideal for dynamic content generation and templating.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Privacy First</h3>
                        <p>All processing happens in your browser. Your code never leaves your device, ensuring complete privacy and security of your content.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>Responsive Design</h3>
                        <p>Works seamlessly across all devices and screen sizes with a mobile-first approach and intuitive touch-friendly interface.</p>
                    </div>
                </div>

                <div class="developer-info">
                    <div class="info-card">
                        <h2>Developer Information</h2>
                        <div class="info-content">
                            <div class="info-section">
                                <h4><i class="fas fa-copyright"></i> Copyright</h4>
                                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
                                <p>This software is provided under the MIT License, allowing free use, modification, and distribution.</p>
                            </div>

                            <div class="info-section">
                                <h4><i class="fas fa-users"></i> Development Team</h4>
                                <p>HTML Pro Tools Development Team</p>
                                <p>A dedicated group of web developers passionate about creating useful tools for the developer community.</p>
                            </div>

                            <div class="info-section">
                                <h4><i class="fas fa-envelope"></i> Contact & Support</h4>
                                <p>For support, feedback, feature requests, or general inquiries:</p>
                                <a href="mailto:<EMAIL>" class="contact-email">
                                    <i class="fas fa-envelope"></i>
                                    <EMAIL>
                                </a>
                                <p style="margin-top: 1rem; font-size: 0.875rem; color: var(--text-muted);">
                                    We typically respond within 24-48 hours during business days.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technology-stack">
                    <h2>Technology Stack</h2>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="tech-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                        <div class="tech-item">
                            <i class="fab fa-js-square"></i>
                            <span>Vanilla JavaScript</span>
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Responsive Design</span>
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Client-Side Processing</span>
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-universal-access"></i>
                            <span>Accessibility</span>
                        </div>
                    </div>
                </div>

                <div class="version-info">
                    <div class="version-card">
                        <h3>Version & Release Information</h3>
                        <div class="version-details">
                            <div class="version-item">
                                <span class="version-label">Current Version:</span>
                                <span class="version-value">v1.0.0</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">Release Date:</span>
                                <span class="version-value">January 2024</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">Last Updated:</span>
                                <span class="version-value">January 2024</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">License:</span>
                                <span class="version-value">MIT License</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">Browser Support:</span>
                                <span class="version-value">Modern Browsers</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>HTML Pro Tools</h4>
                    <p>Professional HTML processing made simple</p>
                </div>
                <div class="footer-section">
                    <h4>Popular Apps</h4>
                    <div class="app-icons">
                        <a href="https://github.com" target="_blank" title="GitHub">
                            <i class="fab fa-github"></i>
                            <span>GitHub</span>
                        </a>
                        <a href="https://codepen.io" target="_blank" title="CodePen">
                            <i class="fab fa-codepen"></i>
                            <span>CodePen</span>
                        </a>
                        <a href="https://stackoverflow.com" target="_blank" title="Stack Overflow">
                            <i class="fab fa-stack-overflow"></i>
                            <span>Stack Overflow</span>
                        </a>
                        <a href="https://developer.mozilla.org" target="_blank" title="MDN Web Docs">
                            <i class="fab fa-firefox-browser"></i>
                            <span>MDN Docs</span>
                        </a>
                        <a href="https://code.visualstudio.com" target="_blank" title="VS Code">
                            <i class="fas fa-code"></i>
                            <span>VS Code</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');

        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    </script>
</body>
</html>
