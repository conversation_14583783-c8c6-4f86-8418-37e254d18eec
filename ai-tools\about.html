<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - HTML Pro Tools</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="about.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-code"></i>
                    <span>HTML Pro Tools</span>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <a href="index.html" class="nav-link">Home</a>
                    <a href="about.html" class="nav-link active">About</a>
                    <a href="privacy.html" class="nav-link">Privacy</a>
                    <a href="terms.html" class="nav-link">Terms</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main class="main">
        <section class="page-hero">
            <div class="container">
                <h1 class="page-title">About HTML Pro Tools</h1>
                <p class="page-subtitle">Professional HTML processing made simple and accessible</p>
            </div>
        </section>

        <section class="about-content">
            <div class="container">
                <div class="content-grid">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3>Our Mission</h3>
                        <p>HTML Pro Tools was created to provide developers, designers, and content creators with powerful, easy-to-use HTML processing capabilities. We believe that code formatting and optimization should be accessible to everyone, regardless of their technical expertise.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3>Features</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> HTML Beautification & Formatting</li>
                            <li><i class="fas fa-check"></i> Code Compression & Minification</li>
                            <li><i class="fas fa-check"></i> External Link Cleaning</li>
                            <li><i class="fas fa-check"></i> CSS Inlining</li>
                            <li><i class="fas fa-check"></i> JavaScript Variable Conversion</li>
                            <li><i class="fas fa-check"></i> Comment Removal</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Privacy & Security</h3>
                        <p>Your code never leaves your browser. All processing is done client-side using JavaScript, ensuring your HTML content remains private and secure. We don't store, transmit, or analyze any of your code.</p>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>Cross-Platform</h3>
                        <p>HTML Pro Tools works seamlessly across all modern browsers and devices. Whether you're on desktop, tablet, or mobile, you'll have access to the same powerful features with a responsive, intuitive interface.</p>
                    </div>
                </div>

                <div class="developer-info">
                    <div class="info-card">
                        <h2>Developer Information</h2>
                        <div class="info-content">
                            <div class="info-section">
                                <h4><i class="fas fa-copyright"></i> Copyright</h4>
                                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
                            </div>
                            
                            <div class="info-section">
                                <h4><i class="fas fa-user-tie"></i> Developer</h4>
                                <p>HTML Pro Tools Development Team</p>
                            </div>
                            
                            <div class="info-section">
                                <h4><i class="fas fa-envelope"></i> Contact</h4>
                                <p>For support, feedback, or inquiries:</p>
                                <a href="mailto:<EMAIL>" class="contact-email">
                                    <i class="fas fa-envelope"></i>
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="technology-stack">
                    <h2>Built With</h2>
                    <div class="tech-grid">
                        <div class="tech-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="tech-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                        <div class="tech-item">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Responsive Design</span>
                        </div>
                    </div>
                </div>

                <div class="version-info">
                    <div class="version-card">
                        <h3>Version Information</h3>
                        <div class="version-details">
                            <div class="version-item">
                                <span class="version-label">Current Version:</span>
                                <span class="version-value">1.0.0</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">Release Date:</span>
                                <span class="version-value">January 2024</span>
                            </div>
                            <div class="version-item">
                                <span class="version-label">Last Updated:</span>
                                <span class="version-value">January 2024</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>HTML Pro Tools</h4>
                    <p>Professional HTML processing made simple</p>
                </div>
                <div class="footer-section">
                    <h4>Popular Apps</h4>
                    <div class="app-icons">
                        <a href="https://github.com" target="_blank" title="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://codepen.io" target="_blank" title="CodePen">
                            <i class="fab fa-codepen"></i>
                        </a>
                        <a href="https://stackoverflow.com" target="_blank" title="Stack Overflow">
                            <i class="fab fa-stack-overflow"></i>
                        </a>
                        <a href="https://developer.mozilla.org" target="_blank" title="MDN Web Docs">
                            <i class="fab fa-firefox-browser"></i>
                        </a>
                        <a href="https://code.visualstudio.com" target="_blank" title="VS Code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 HTML Pro Tools. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');

        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
    </script>
</body>
</html>
