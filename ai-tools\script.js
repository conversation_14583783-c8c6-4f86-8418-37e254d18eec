// DOM Elements
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const htmlInput = document.getElementById('htmlInput');
const htmlOutput = document.getElementById('htmlOutput');
const processBtn = document.getElementById('processHTML');
const clearInputBtn = document.getElementById('clearInput');
const loadSampleBtn = document.getElementById('loadSample');
const copyOutputBtn = document.getElementById('copyOutput');
const downloadOutputBtn = document.getElementById('downloadOutput');
const outputStats = document.getElementById('outputStats');
const jsVarOptions = document.getElementById('jsVarOptions');
const toJSVariableCheckbox = document.getElementById('toJSVariable');

// Navigation Toggle
navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
    });
});

// Show/Hide JS Variable Options
toJSVariableCheckbox.addEventListener('change', (e) => {
    jsVarOptions.style.display = e.target.checked ? 'flex' : 'none';
});

// Sample HTML
const sampleHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #333; color: white; padding: 1rem; }
        .content { padding: 2rem 0; }
    </style>
</head>
<body>
    <!-- This is a sample HTML document -->
    <div class="container">
        <header class="header">
            <h1>Welcome to Sample Page</h1>
        </header>
        <main class="content">
            <p>This is a sample HTML document with some content.</p>
            <a href="https://example.com" target="_blank">External Link</a>
            <p>More content here with <strong>bold text</strong> and <em>italic text</em>.</p>
        </main>
    </div>
</body>
</html>`;

// Load Sample HTML
loadSampleBtn.addEventListener('click', () => {
    htmlInput.value = sampleHTML;
    updateStats();
});

// Clear Input
clearInputBtn.addEventListener('click', () => {
    htmlInput.value = '';
    htmlOutput.value = '';
    outputStats.classList.remove('show');
});

// HTML Processing Functions
function beautifyHTML(html) {
    let formatted = html;
    let indent = 0;
    const indentSize = 2;
    
    // Remove extra whitespace
    formatted = formatted.replace(/>\s+</g, '><');
    
    // Add line breaks and indentation
    formatted = formatted.replace(/></g, '>\n<');
    
    const lines = formatted.split('\n');
    const result = [];
    
    lines.forEach(line => {
        const trimmed = line.trim();
        if (!trimmed) return;
        
        // Decrease indent for closing tags
        if (trimmed.startsWith('</') && !trimmed.includes('/>')) {
            indent = Math.max(0, indent - indentSize);
        }
        
        // Add indentation
        result.push(' '.repeat(indent) + trimmed);
        
        // Increase indent for opening tags (but not self-closing or closing tags)
        if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.includes('/>') && !trimmed.includes('<!')) {
            // Check if it's not a self-closing tag
            const tagName = trimmed.match(/<(\w+)/);
            if (tagName) {
                const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
                if (!selfClosingTags.includes(tagName[1].toLowerCase())) {
                    indent += indentSize;
                }
            }
        }
    });
    
    return result.join('\n');
}

function compressHTML(html) {
    return html
        .replace(/\s+/g, ' ')
        .replace(/>\s+</g, '><')
        .replace(/\s+>/g, '>')
        .replace(/<\s+/g, '<')
        .trim();
}

function removeComments(html) {
    return html.replace(/<!--[\s\S]*?-->/g, '');
}

function removeExternalLinks(html) {
    // Remove href attributes that start with http:// or https://
    return html.replace(/href\s*=\s*["']https?:\/\/[^"']*["']/gi, 'href="#"');
}

function inlineCSS(html) {
    // Extract CSS from style tags
    const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
    const styles = [];
    let match;
    
    while ((match = styleRegex.exec(html)) !== null) {
        styles.push(match[1]);
    }
    
    if (styles.length === 0) return html;
    
    // Simple CSS parser for basic selectors
    const cssRules = {};
    styles.forEach(style => {
        const rules = style.split('}');
        rules.forEach(rule => {
            const parts = rule.split('{');
            if (parts.length === 2) {
                const selector = parts[0].trim();
                const declarations = parts[1].trim();
                if (selector && declarations) {
                    cssRules[selector] = declarations;
                }
            }
        });
    });
    
    // Apply styles inline (basic implementation)
    let result = html;
    
    // Remove style tags
    result = result.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    // Apply basic class and tag styles
    Object.keys(cssRules).forEach(selector => {
        if (selector.startsWith('.')) {
            const className = selector.substring(1);
            const regex = new RegExp(`class\\s*=\\s*["']([^"']*\\b${className}\\b[^"']*)["']`, 'gi');
            result = result.replace(regex, (match, classes) => {
                return match.replace('>', ` style="${cssRules[selector]}">`);
            });
        } else if (!selector.includes(' ') && !selector.includes(':') && !selector.includes('#')) {
            // Simple tag selector
            const regex = new RegExp(`<${selector}([^>]*)>`, 'gi');
            result = result.replace(regex, (match, attrs) => {
                if (attrs.includes('style=')) {
                    return match.replace(/style\s*=\s*["']([^"']*)["']/, `style="$1; ${cssRules[selector]}"`);
                } else {
                    return `<${selector}${attrs} style="${cssRules[selector]}">`;
                }
            });
        }
    });
    
    return result;
}

function convertToJSVariable(html, varName = 'htmlContent', varType = 'const') {
    // Escape quotes and handle line breaks
    const escaped = html
        .replace(/\\/g, '\\\\')
        .replace(/'/g, "\\'")
        .replace(/"/g, '\\"')
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t');
    
    return `${varType} ${varName} = "${escaped}";`;
}

// Process HTML
function processHTML() {
    const input = htmlInput.value.trim();
    if (!input) {
        alert('Please enter some HTML code to process.');
        return;
    }
    
    // Show processing state
    document.querySelector('.options-card').classList.add('processing');
    
    setTimeout(() => {
        let result = input;
        
        // Get options
        const beautify = document.getElementById('beautify').checked;
        const compress = document.getElementById('compress').checked;
        const removeLinks = document.getElementById('removeLinks').checked;
        const removeCommentsOption = document.getElementById('removeComments').checked;
        const inlineCSSOption = document.getElementById('inlineCSS').checked;
        const toJSVariable = document.getElementById('toJSVariable').checked;
        
        // Apply processing options
        if (removeCommentsOption) {
            result = removeComments(result);
        }
        
        if (removeLinks) {
            result = removeExternalLinks(result);
        }
        
        if (inlineCSSOption) {
            result = inlineCSS(result);
        }
        
        if (compress) {
            result = compressHTML(result);
        } else if (beautify) {
            result = beautifyHTML(result);
        }
        
        if (toJSVariable) {
            const varName = document.getElementById('varName').value || 'htmlContent';
            const varType = document.getElementById('varType').value;
            result = convertToJSVariable(result, varName, varType);
        }
        
        htmlOutput.value = result;
        updateStats();
        
        // Remove processing state
        document.querySelector('.options-card').classList.remove('processing');
    }, 500);
}

// Update Statistics
function updateStats() {
    const input = htmlInput.value;
    const output = htmlOutput.value;
    
    if (output) {
        const inputSize = new Blob([input]).size;
        const outputSize = new Blob([output]).size;
        const compression = inputSize > 0 ? ((inputSize - outputSize) / inputSize * 100).toFixed(1) : 0;
        
        outputStats.innerHTML = `
            <strong>Statistics:</strong><br>
            Input size: ${inputSize} bytes<br>
            Output size: ${outputSize} bytes<br>
            ${compression > 0 ? `Compression: ${compression}%` : compression < 0 ? `Expansion: ${Math.abs(compression)}%` : 'No size change'}
        `;
        outputStats.classList.add('show');
    } else {
        outputStats.classList.remove('show');
    }
}

// Copy to Clipboard
async function copyToClipboard() {
    if (!htmlOutput.value) {
        alert('No output to copy.');
        return;
    }
    
    try {
        await navigator.clipboard.writeText(htmlOutput.value);
        
        // Visual feedback
        const originalText = copyOutputBtn.innerHTML;
        copyOutputBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyOutputBtn.style.background = 'var(--success-color)';
        
        setTimeout(() => {
            copyOutputBtn.innerHTML = originalText;
            copyOutputBtn.style.background = '';
        }, 2000);
    } catch (err) {
        // Fallback for older browsers
        htmlOutput.select();
        document.execCommand('copy');
        alert('Output copied to clipboard!');
    }
}

// Download Output
function downloadOutput() {
    if (!htmlOutput.value) {
        alert('No output to download.');
        return;
    }
    
    const toJSVariable = document.getElementById('toJSVariable').checked;
    const filename = toJSVariable ? 'output.js' : 'output.html';
    const mimeType = toJSVariable ? 'application/javascript' : 'text/html';
    
    const blob = new Blob([htmlOutput.value], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Event Listeners
processBtn.addEventListener('click', processHTML);
copyOutputBtn.addEventListener('click', copyToClipboard);
downloadOutputBtn.addEventListener('click', downloadOutput);

// Auto-update stats when input changes
htmlInput.addEventListener('input', updateStats);

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'Enter':
                e.preventDefault();
                processHTML();
                break;
            case 'k':
                e.preventDefault();
                htmlInput.value = '';
                htmlOutput.value = '';
                outputStats.classList.remove('show');
                htmlInput.focus();
                break;
        }
    }
});

// Initialize
updateStats();
