/* Legal Pages Enhanced Styles */

.page-hero {
    padding: 4rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.page-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.page-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 1.5rem;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.last-updated {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
    backdrop-filter: blur(10px);
}

.legal-content {
    padding: 6rem 0;
    background: var(--bg-primary);
    position: relative;
}

.legal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 40%);
    pointer-events: none;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 4rem;
    align-items: start;
    position: relative;
    z-index: 1;
}

.toc {
    position: sticky;
    top: 120px;
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 20px;
    padding: 2rem;
    height: fit-content;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-lg);
}

.toc::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 20px 20px 0 0;
}

.toc h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.toc ul {
    list-style: none;
}

.toc li {
    margin-bottom: 0.75rem;
}

.toc a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    display: block;
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
}

.toc a:hover {
    color: var(--text-primary);
    background: rgba(99, 102, 241, 0.1);
    transform: translateX(4px);
}

.legal-document {
    background: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    border: 1px solid var(--border-light);
    border-radius: 24px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-xl);
}

.legal-document::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 24px 24px 0 0;
}

.legal-section {
    margin-bottom: 4rem;
    scroll-margin-top: 120px;
    position: relative;
}

.legal-section:last-child {
    margin-bottom: 0;
}

.legal-section h2 {
    color: var(--text-primary);
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
    position: relative;
}

.legal-section h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 1px;
}

.legal-section h2 i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
}

.legal-section h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.legal-section p {
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.legal-section ul {
    margin: 1.5rem 0;
    padding-left: 0;
}

.legal-section li {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 0.75rem;
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    list-style: none;
    position: relative;
    transition: all 0.3s ease;
}

.legal-section li:hover {
    background: rgba(99, 102, 241, 0.05);
    transform: translateX(4px);
}

.legal-section li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: -0.5rem;
    top: 0.75rem;
}

.legal-section li strong {
    color: var(--text-primary);
    font-weight: 600;
}

.highlight-box {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.highlight-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

.highlight-box.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.highlight-box.success::before {
    background: var(--success-color);
}

.highlight-box.warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
}

.highlight-box.warning::before {
    background: var(--warning-color);
}

.highlight-box i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.highlight-box.success i {
    color: var(--success-color);
    background: none;
    -webkit-text-fill-color: var(--success-color);
}

.highlight-box.warning i {
    color: var(--warning-color);
    background: none;
    -webkit-text-fill-color: var(--warning-color);
}

.highlight-box p {
    margin: 0;
    color: var(--text-primary);
    font-weight: 500;
}

.contact-info {
    background: var(--bg-primary);
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    position: relative;
}

.contact-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-accent);
    border-radius: 16px 0 0 16px;
}

.contact-info p {
    margin-bottom: 1rem;
    font-weight: 500;
}

.contact-info a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-wrapper {
        grid-template-columns: 250px 1fr;
        gap: 3rem;
    }
    
    .toc {
        padding: 1.5rem;
    }
    
    .legal-document {
        padding: 2.5rem;
    }
}

@media (max-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 200px 1fr;
        gap: 2rem;
    }
    
    .toc {
        padding: 1.25rem;
    }
    
    .legal-document {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .toc {
        position: static;
        order: 2;
        margin-top: 3rem;
    }
    
    .legal-document {
        order: 1;
        padding: 1.5rem;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .legal-section h2 {
        font-size: 1.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .highlight-box {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .highlight-box i {
        align-self: flex-start;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .legal-content {
        padding: 3rem 0;
    }
    
    .legal-document {
        padding: 1.25rem;
    }
    
    .legal-section {
        margin-bottom: 3rem;
    }
    
    .legal-section h2 {
        font-size: 1.25rem;
    }
    
    .highlight-box {
        padding: 1.25rem;
    }
    
    .contact-info {
        padding: 1.5rem;
    }
}

/* Enhanced Animations */
.legal-section {
    animation: fadeInUp 0.8s ease-out;
}

.legal-section:nth-child(2) {
    animation-delay: 0.1s;
}

.legal-section:nth-child(3) {
    animation-delay: 0.2s;
}

.legal-section:nth-child(4) {
    animation-delay: 0.3s;
}

.legal-section:nth-child(5) {
    animation-delay: 0.4s;
}

.toc {
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.toc a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 8px;
}

/* Print styles */
@media print {
    .header,
    .footer,
    .toc {
        display: none;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .legal-document {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .page-hero {
        background: none;
        color: #000;
    }
    
    .page-title {
        color: #000;
        background: none;
        -webkit-text-fill-color: initial;
    }
}
