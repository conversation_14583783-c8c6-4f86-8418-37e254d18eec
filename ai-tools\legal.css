/* Legal Pages Specific Styles */

.page-hero {
    padding: 3rem 0;
    background: var(--gradient-primary);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.page-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 500px;
    margin: 0 auto 1rem;
    position: relative;
    z-index: 1;
}

.last-updated {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
}

.legal-content {
    padding: 4rem 0;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 3rem;
    align-items: start;
}

.toc {
    position: sticky;
    top: 100px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    height: fit-content;
}

.toc h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.toc ul {
    list-style: none;
}

.toc li {
    margin-bottom: 0.5rem;
}

.toc a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0.25rem 0;
    display: block;
    transition: color 0.3s ease;
}

.toc a:hover {
    color: var(--primary-color);
}

.legal-document {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.legal-document::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.legal-section {
    margin-bottom: 3rem;
    scroll-margin-top: 100px;
}

.legal-section:last-child {
    margin-bottom: 0;
}

.legal-section h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--border-color);
}

.legal-section h2 i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.legal-section h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem;
}

.legal-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.legal-section ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.legal-section li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.legal-section li strong {
    color: var(--text-primary);
}

.highlight-box {
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 0.75rem;
    padding: 1.25rem;
    margin: 1.5rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.highlight-box.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.highlight-box.warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
}

.highlight-box i {
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.highlight-box.success i {
    color: var(--success-color);
}

.highlight-box.warning i {
    color: var(--warning-color);
}

.highlight-box p {
    margin: 0;
    color: var(--text-primary);
}

.contact-info {
    background: var(--bg-primary);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.contact-info p {
    margin-bottom: 0.5rem;
}

.contact-info a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 200px 1fr;
        gap: 2rem;
    }
    
    .toc {
        padding: 1rem;
    }
    
    .legal-document {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .toc {
        position: static;
        order: 2;
        margin-top: 2rem;
    }
    
    .legal-document {
        order: 1;
        padding: 1.25rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .legal-section h2 {
        font-size: 1.25rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .highlight-box {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .highlight-box i {
        align-self: flex-start;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.75rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .legal-content {
        padding: 2rem 0;
    }
    
    .legal-document {
        padding: 1rem;
    }
    
    .legal-section {
        margin-bottom: 2rem;
    }
    
    .legal-section h2 {
        font-size: 1.125rem;
    }
    
    .highlight-box {
        padding: 1rem;
    }
    
    .contact-info {
        padding: 1rem;
    }
}

/* Animation */
.legal-section {
    animation: fadeInUp 0.6s ease-out;
}

.legal-section:nth-child(2) {
    animation-delay: 0.1s;
}

.legal-section:nth-child(3) {
    animation-delay: 0.2s;
}

.legal-section:nth-child(4) {
    animation-delay: 0.3s;
}

.toc {
    animation: fadeInUp 0.6s ease-out 0.2s both;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.toc a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: 0.25rem;
}

/* Print styles */
@media print {
    .header,
    .footer,
    .toc {
        display: none;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .legal-document {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .page-hero {
        background: none;
        color: #000;
    }
    
    .page-title {
        color: #000;
        background: none;
        -webkit-text-fill-color: initial;
    }
}
