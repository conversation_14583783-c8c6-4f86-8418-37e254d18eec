// Pollinations.AI API 示例代码

// 图片生成
const generateImage = async (prompt) => {
  const response = await fetch(`https://image.pollinations.ai/prompt/${encodeURIComponent(prompt)}`);
  return response.url;
};

// 获取图片模型列表
const getImageModels = async () => {
  const response = await fetch('https://image.pollinations.ai/models');
  return response.json();
};

// 文本生成 (GET)
const generateTextGET = async (prompt) => {
  const response = await fetch(`https://text.pollinations.ai/${encodeURIComponent(prompt)}`);
  return response.text();
};

// 文本生成 (POST)
const generateTextPOST = async (prompt) => {
  const response = await fetch('https://text.pollinations.ai/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt }),
  });
  return response.text();
};

// 音频生成
const generateAudio = async (prompt, voice = 'default') => {
  const response = await fetch(`https://audio.pollinations.ai/prompt/${encodeURIComponent(prompt)}?voice=${voice}`);
  return response.url;
};

// OpenAI 兼容端点
const openAICompatible = async (prompt) => {
  try {
    const response = await fetch('https://text.pollinations.ai/openai', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt }),
    });
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  } catch (error) {
    console.error('Error calling OpenAI compatible endpoint:', error);
    throw error;
  }
};

// 获取文本模型列表
const getTextModels = async () => {
  const response = await fetch('https://text.pollinations.ai/models');
  return response.json();
};

// 实时图片流
const getImageFeed = async () => {
  const response = await fetch('https://image.pollinations.ai/feed');
  return response.json();
};

// 实时文本流
const getTextFeed = async () => {
  const response = await fetch('https://text.pollinations.ai/feed');
  return response.json();
};

// 示例调用
(async () => {
  const imageUrl = await generateImage('a beautiful sunset');
  console.log('Generated Image URL:', imageUrl);

  const text = await generateTextGET('Hello, world!');
  console.log('Generated Text:', text);

  const audioUrl = await generateAudio('Hello, this is a test audio', 'default');
  console.log('Generated Audio URL:', audioUrl);

  const audioUrl2 = await generateAudio('Hello, this is a test audio');
  console.log('Generated Audio URL:', audioUrl2);
})();