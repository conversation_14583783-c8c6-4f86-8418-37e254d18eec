/* About Page Specific Styles */

.page-hero {
    padding: 3rem 0;
    background: var(--gradient-primary);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.page-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.about-content {
    padding: 4rem 0;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.content-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.content-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.content-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    text-align: left;
    margin-top: 1rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.feature-list i {
    color: var(--success-color);
    font-size: 0.875rem;
}

.developer-info {
    margin-bottom: 4rem;
}

.info-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
}

.info-card h2 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.info-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.info-section h4 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-section h4 i {
    color: var(--primary-color);
}

.info-section p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.contact-email {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.contact-email:hover {
    background: rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
}

.technology-stack {
    margin-bottom: 4rem;
    text-align: center;
}

.technology-stack h2 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-3px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.tech-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.tech-item span {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
}

.version-info {
    display: flex;
    justify-content: center;
}

.version-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 400px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.version-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.version-card h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
}

.version-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.version-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 0.5rem;
}

.version-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.version-value {
    color: var(--text-primary);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .info-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .content-card,
    .info-card,
    .version-card {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.75rem;
    }

    .page-subtitle {
        font-size: 1rem;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .version-item {
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
    }
}

/* Animation */
.content-card {
    animation: fadeInUp 0.6s ease-out;
}

.content-card:nth-child(2) {
    animation-delay: 0.1s;
}

.content-card:nth-child(3) {
    animation-delay: 0.2s;
}

.content-card:nth-child(4) {
    animation-delay: 0.3s;
}

.info-card {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

.technology-stack {
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

.version-info {
    animation: fadeInUp 0.6s ease-out 0.6s both;
}
