// DOM Elements
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const htmlInput = document.getElementById('htmlInput');
const htmlOutput = document.getElementById('htmlOutput');
const processBtn = document.getElementById('processHTML');
const clearInputBtn = document.getElementById('clearInput');
const loadSampleBtn = document.getElementById('loadSample');
const copyOutputBtn = document.getElementById('copyOutput');
const downloadOutputBtn = document.getElementById('downloadOutput');
const outputStats = document.getElementById('outputStats');
const jsVarOptions = document.getElementById('jsVarOptions');
const toJSVariableCheckbox = document.getElementById('toJSVariable');

// Navigation Toggle
navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
    });
});

// Close mobile menu when clicking outside
document.addEventListener('click', (e) => {
    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
    }
});

// Show/Hide JS Variable Options
toJSVariableCheckbox.addEventListener('change', (e) => {
    jsVarOptions.style.display = e.target.checked ? 'flex' : 'none';
});

// Enhanced Sample HTML
const sampleHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample HTML Document</title>
    <style>
        body { 
            font-family: 'Arial', sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header { 
            background: #333; 
            color: white; 
            padding: 2rem; 
            text-align: center;
        }
        .content { 
            padding: 3rem 2rem; 
            line-height: 1.6;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #764ba2;
            transform: translateY(-2px);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .card {
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
    </style>
</head>
<body>
    <!-- This is a comprehensive sample HTML document -->
    <div class="container">
        <header class="header">
            <h1>Welcome to HTML Pro Tools Demo</h1>
            <p>Experience the power of professional HTML processing</p>
        </header>
        <main class="content">
            <section>
                <h2>About This Tool</h2>
                <p>This is a sample HTML document designed to showcase the capabilities of our HTML processing tools. It includes various elements like <strong>bold text</strong>, <em>italic text</em>, and <a href="https://example.com" target="_blank">external links</a>.</p>
                
                <div class="grid">
                    <div class="card">
                        <h3>Feature 1</h3>
                        <p>HTML beautification with proper indentation and formatting.</p>
                    </div>
                    <div class="card">
                        <h3>Feature 2</h3>
                        <p>Code compression to reduce file size and improve performance.</p>
                    </div>
                    <div class="card">
                        <h3>Feature 3</h3>
                        <p>CSS inlining for better email template compatibility.</p>
                    </div>
                </div>
                
                <p>Try our tools with this sample or paste your own HTML code!</p>
                <a href="#" class="button">Get Started</a>
            </section>
        </main>
    </div>
    
    <script>
        // Sample JavaScript code
        console.log('HTML Pro Tools Demo Loaded');
        
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.button');
            button.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Welcome to HTML Pro Tools!');
            });
        });
    </script>
</body>
</html>`;

// Load Sample HTML
loadSampleBtn.addEventListener('click', () => {
    htmlInput.value = sampleHTML;
    updateStats();
    
    // Visual feedback
    const originalText = loadSampleBtn.innerHTML;
    loadSampleBtn.innerHTML = '<i class="fas fa-check"></i> Loaded!';
    loadSampleBtn.style.background = 'var(--success-color)';
    
    setTimeout(() => {
        loadSampleBtn.innerHTML = originalText;
        loadSampleBtn.style.background = '';
    }, 2000);
});

// Clear Input
clearInputBtn.addEventListener('click', () => {
    if (htmlInput.value && !confirm('Are you sure you want to clear all content?')) {
        return;
    }
    
    htmlInput.value = '';
    htmlOutput.value = '';
    outputStats.classList.remove('show');
    htmlInput.focus();
    
    // Visual feedback
    const originalText = clearInputBtn.innerHTML;
    clearInputBtn.innerHTML = '<i class="fas fa-check"></i> Cleared!';
    clearInputBtn.style.background = 'var(--success-color)';
    
    setTimeout(() => {
        clearInputBtn.innerHTML = originalText;
        clearInputBtn.style.background = '';
    }, 1500);
});

// Enhanced HTML Processing Functions
function beautifyHTML(html) {
    let formatted = html;
    let indent = 0;
    const indentSize = 2;
    
    // Remove extra whitespace but preserve content
    formatted = formatted.replace(/>\s+</g, '><');
    
    // Add line breaks and indentation
    formatted = formatted.replace(/></g, '>\n<');
    
    const lines = formatted.split('\n');
    const result = [];
    
    lines.forEach(line => {
        const trimmed = line.trim();
        if (!trimmed) return;
        
        // Decrease indent for closing tags
        if (trimmed.startsWith('</') && !trimmed.includes('/>')) {
            indent = Math.max(0, indent - indentSize);
        }
        
        // Add indentation
        result.push(' '.repeat(indent) + trimmed);
        
        // Increase indent for opening tags (but not self-closing or closing tags)
        if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.includes('/>') && !trimmed.includes('<!')) {
            // Check if it's not a self-closing tag
            const tagName = trimmed.match(/<(\w+)/);
            if (tagName) {
                const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
                if (!selfClosingTags.includes(tagName[1].toLowerCase())) {
                    indent += indentSize;
                }
            }
        }
    });
    
    return result.join('\n');
}

function compressHTML(html) {
    return html
        // Remove comments (but preserve conditional comments)
        .replace(/<!--(?!\[if)[\s\S]*?-->/g, '')
        // Remove extra whitespace
        .replace(/\s+/g, ' ')
        // Remove whitespace between tags
        .replace(/>\s+</g, '><')
        // Remove whitespace around tags
        .replace(/\s+>/g, '>')
        .replace(/<\s+/g, '<')
        // Remove quotes around single-word attributes
        .replace(/=["']([a-zA-Z0-9-_]+)["']/g, '=$1')
        .trim();
}

function removeComments(html) {
    // Remove HTML comments but preserve conditional comments for IE
    return html.replace(/<!--(?!\[if)(?!<!)\s*(?!>)[\s\S]*?-->/g, '');
}

function removeExternalLinks(html) {
    // Remove href attributes that start with http:// or https://
    return html.replace(/href\s*=\s*["']https?:\/\/[^"']*["']/gi, 'href="#"');
}

function inlineCSS(html) {
    // Extract CSS from style tags
    const styleRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi;
    const styles = [];
    let match;
    
    while ((match = styleRegex.exec(html)) !== null) {
        styles.push(match[1]);
    }
    
    if (styles.length === 0) return html;
    
    // Enhanced CSS parser for better selector support
    const cssRules = {};
    styles.forEach(style => {
        // Remove comments from CSS
        style = style.replace(/\/\*[\s\S]*?\*\//g, '');
        
        const rules = style.split('}');
        rules.forEach(rule => {
            const parts = rule.split('{');
            if (parts.length === 2) {
                const selector = parts[0].trim();
                const declarations = parts[1].trim();
                if (selector && declarations) {
                    cssRules[selector] = declarations;
                }
            }
        });
    });
    
    // Apply styles inline (enhanced implementation)
    let result = html;
    
    // Remove style tags
    result = result.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
    
    // Apply basic class and tag styles
    Object.keys(cssRules).forEach(selector => {
        if (selector.startsWith('.')) {
            // Class selector
            const className = selector.substring(1);
            const regex = new RegExp(`class\\s*=\\s*["']([^"']*\\b${className}\\b[^"']*)["']`, 'gi');
            result = result.replace(regex, (match, classes) => {
                const tagMatch = result.substring(result.lastIndexOf('<', result.indexOf(match)), result.indexOf('>', result.indexOf(match)) + 1);
                if (tagMatch.includes('style=')) {
                    return match.replace(/style\s*=\s*["']([^"']*)["']/, `style="$1; ${cssRules[selector]}"`);
                } else {
                    return match + ` style="${cssRules[selector]}"`;
                }
            });
        } else if (!selector.includes(' ') && !selector.includes(':') && !selector.includes('#') && !selector.includes('[')) {
            // Simple tag selector
            const regex = new RegExp(`<${selector}([^>]*)>`, 'gi');
            result = result.replace(regex, (match, attrs) => {
                if (attrs.includes('style=')) {
                    return match.replace(/style\s*=\s*["']([^"']*)["']/, `style="$1; ${cssRules[selector]}"`);
                } else {
                    return `<${selector}${attrs} style="${cssRules[selector]}">`;
                }
            });
        }
    });
    
    return result;
}

function convertToJSVariable(html, varName = 'htmlContent', varType = 'const') {
    // Enhanced escaping for JavaScript
    const escaped = html
        .replace(/\\/g, '\\\\')
        .replace(/'/g, "\\'")
        .replace(/"/g, '\\"')
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t')
        .replace(/\f/g, '\\f')
        .replace(/\v/g, '\\v');
    
    return `${varType} ${varName} = "${escaped}";`;
}

// Enhanced Process HTML
function processHTML() {
    const input = htmlInput.value.trim();
    if (!input) {
        showNotification('Please enter some HTML code to process.', 'warning');
        htmlInput.focus();
        return;
    }
    
    // Show processing state
    document.querySelector('.options-card').classList.add('processing');
    processBtn.disabled = true;
    
    setTimeout(() => {
        try {
            let result = input;
            
            // Get options
            const beautify = document.getElementById('beautify').checked;
            const compress = document.getElementById('compress').checked;
            const removeLinks = document.getElementById('removeLinks').checked;
            const removeCommentsOption = document.getElementById('removeComments').checked;
            const inlineCSSOption = document.getElementById('inlineCSS').checked;
            const toJSVariable = document.getElementById('toJSVariable').checked;
            
            // Apply processing options in order
            if (removeCommentsOption) {
                result = removeComments(result);
            }
            
            if (removeLinks) {
                result = removeExternalLinks(result);
            }
            
            if (inlineCSSOption) {
                result = inlineCSS(result);
            }
            
            if (compress) {
                result = compressHTML(result);
            } else if (beautify) {
                result = beautifyHTML(result);
            }
            
            if (toJSVariable) {
                const varName = document.getElementById('varName').value || 'htmlContent';
                const varType = document.getElementById('varType').value;
                result = convertToJSVariable(result, varName, varType);
            }
            
            htmlOutput.value = result;
            updateStats();
            showNotification('HTML processed successfully!', 'success');
            
        } catch (error) {
            console.error('Processing error:', error);
            showNotification('An error occurred while processing HTML.', 'error');
        } finally {
            // Remove processing state
            document.querySelector('.options-card').classList.remove('processing');
            processBtn.disabled = false;
        }
    }, 800);
}

// Enhanced Statistics
function updateStats() {
    const input = htmlInput.value;
    const output = htmlOutput.value;
    
    if (output) {
        const inputSize = new Blob([input]).size;
        const outputSize = new Blob([output]).size;
        const compression = inputSize > 0 ? ((inputSize - outputSize) / inputSize * 100).toFixed(1) : 0;
        const inputLines = input.split('\n').length;
        const outputLines = output.split('\n').length;
        
        let compressionText = '';
        if (compression > 0) {
            compressionText = `<span style="color: var(--success-color)">Compression: ${compression}%</span>`;
        } else if (compression < 0) {
            compressionText = `<span style="color: var(--warning-color)">Expansion: ${Math.abs(compression)}%</span>`;
        } else {
            compressionText = 'No size change';
        }
        
        outputStats.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; text-align: center;">
                <div>
                    <strong style="color: var(--text-primary);">${inputSize}</strong><br>
                    <small>Input bytes</small>
                </div>
                <div>
                    <strong style="color: var(--text-primary);">${outputSize}</strong><br>
                    <small>Output bytes</small>
                </div>
                <div>
                    <strong style="color: var(--text-primary);">${inputLines}</strong><br>
                    <small>Input lines</small>
                </div>
                <div>
                    <strong style="color: var(--text-primary);">${outputLines}</strong><br>
                    <small>Output lines</small>
                </div>
                <div style="grid-column: 1 / -1;">
                    ${compressionText}
                </div>
            </div>
        `;
        outputStats.classList.add('show');
    } else {
        outputStats.classList.remove('show');
    }
}

// Enhanced Copy to Clipboard
async function copyToClipboard() {
    if (!htmlOutput.value) {
        showNotification('No output to copy.', 'warning');
        return;
    }
    
    try {
        await navigator.clipboard.writeText(htmlOutput.value);
        showNotification('Output copied to clipboard!', 'success');
        
        // Visual feedback
        const originalText = copyOutputBtn.innerHTML;
        copyOutputBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyOutputBtn.style.background = 'var(--success-color)';
        
        setTimeout(() => {
            copyOutputBtn.innerHTML = originalText;
            copyOutputBtn.style.background = '';
        }, 2000);
    } catch (err) {
        // Fallback for older browsers
        htmlOutput.select();
        document.execCommand('copy');
        showNotification('Output copied to clipboard!', 'success');
    }
}

// Enhanced Download Output
function downloadOutput() {
    if (!htmlOutput.value) {
        showNotification('No output to download.', 'warning');
        return;
    }
    
    const toJSVariable = document.getElementById('toJSVariable').checked;
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = toJSVariable ? `html-output-${timestamp}.js` : `html-output-${timestamp}.html`;
    const mimeType = toJSVariable ? 'application/javascript' : 'text/html';
    
    const blob = new Blob([htmlOutput.value], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification(`File downloaded as ${filename}`, 'success');
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add notification styles if not already present
    if (!document.querySelector('#notification-styles')) {
        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notification {
                position: fixed;
                top: 100px;
                right: 2rem;
                background: var(--bg-secondary);
                border: 1px solid var(--border-light);
                border-radius: 12px;
                padding: 1rem 1.5rem;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                z-index: 10000;
                backdrop-filter: blur(10px);
                box-shadow: var(--shadow-xl);
                transform: translateX(400px);
                transition: transform 0.3s ease;
            }
            .notification.show {
                transform: translateX(0);
            }
            .notification-success { border-left: 4px solid var(--success-color); }
            .notification-warning { border-left: 4px solid var(--warning-color); }
            .notification-error { border-left: 4px solid var(--error-color); }
            .notification-info { border-left: 4px solid var(--primary-color); }
            .notification i {
                color: var(--primary-color);
            }
            .notification-success i { color: var(--success-color); }
            .notification-warning i { color: var(--warning-color); }
            .notification-error i { color: var(--error-color); }
            .notification span {
                color: var(--text-primary);
                font-weight: 500;
            }
        `;
        document.head.appendChild(styles);
    }
    
    document.body.appendChild(notification);
    
    // Trigger animation
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Event Listeners
processBtn.addEventListener('click', processHTML);
copyOutputBtn.addEventListener('click', copyToClipboard);
downloadOutputBtn.addEventListener('click', downloadOutput);

// Auto-update stats when input changes
htmlInput.addEventListener('input', updateStats);

// Enhanced Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'Enter':
                e.preventDefault();
                processHTML();
                break;
            case 'k':
                e.preventDefault();
                clearInputBtn.click();
                break;
            case 's':
                e.preventDefault();
                if (htmlOutput.value) {
                    downloadOutput();
                }
                break;
        }
    }
});

// Auto-save to localStorage
function saveToLocalStorage() {
    localStorage.setItem('htmlProTools_input', htmlInput.value);
    localStorage.setItem('htmlProTools_options', JSON.stringify({
        beautify: document.getElementById('beautify').checked,
        compress: document.getElementById('compress').checked,
        removeLinks: document.getElementById('removeLinks').checked,
        removeComments: document.getElementById('removeComments').checked,
        inlineCSS: document.getElementById('inlineCSS').checked,
        toJSVariable: document.getElementById('toJSVariable').checked,
        varName: document.getElementById('varName').value,
        varType: document.getElementById('varType').value
    }));
}

function loadFromLocalStorage() {
    const savedInput = localStorage.getItem('htmlProTools_input');
    const savedOptions = localStorage.getItem('htmlProTools_options');
    
    if (savedInput) {
        htmlInput.value = savedInput;
        updateStats();
    }
    
    if (savedOptions) {
        try {
            const options = JSON.parse(savedOptions);
            document.getElementById('beautify').checked = options.beautify ?? true;
            document.getElementById('compress').checked = options.compress ?? false;
            document.getElementById('removeLinks').checked = options.removeLinks ?? false;
            document.getElementById('removeComments').checked = options.removeComments ?? false;
            document.getElementById('inlineCSS').checked = options.inlineCSS ?? false;
            document.getElementById('toJSVariable').checked = options.toJSVariable ?? false;
            document.getElementById('varName').value = options.varName || 'htmlContent';
            document.getElementById('varType').value = options.varType || 'const';
            
            // Update JS variable options visibility
            jsVarOptions.style.display = options.toJSVariable ? 'flex' : 'none';
        } catch (e) {
            console.warn('Failed to load saved options:', e);
        }
    }
}

// Save options when they change
document.querySelectorAll('input, select').forEach(element => {
    element.addEventListener('change', saveToLocalStorage);
});

// Save input periodically
setInterval(() => {
    if (htmlInput.value !== localStorage.getItem('htmlProTools_input')) {
        saveToLocalStorage();
    }
}, 5000);

// Create floating particles
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles';
    document.body.appendChild(particlesContainer);

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 20 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Add smooth scroll behavior
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Add intersection observer for animations
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, {
        threshold: 0.1
    });

    document.querySelectorAll('.tool-card, .option-group').forEach(el => {
        observer.observe(el);
    });
}

// Enhanced page load effect
function initializePageEffects() {
    // Add loading class initially
    document.body.classList.add('loading');

    // Remove loading class after delay
    setTimeout(() => {
        document.body.classList.remove('loading');
        document.body.classList.add('loaded');
    }, 1000);

    // Create particles
    createParticles();

    // Add smooth scrolling
    addSmoothScrolling();

    // Add scroll animations
    addScrollAnimations();
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadFromLocalStorage();
    updateStats();

    // Initialize page effects
    initializePageEffects();

    // Focus on input after loading
    setTimeout(() => {
        htmlInput.focus();
    }, 1200);
});
