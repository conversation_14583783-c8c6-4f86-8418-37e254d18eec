/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8b5cf6;
    --primary-dark: #7c3aed;
    --secondary-color: #06b6d4;
    --accent-color: #f59e0b;
    --accent-pink: #ec4899;
    --accent-green: #10b981;
    --bg-primary: #0c0a1a;
    --bg-secondary: #1a1625;
    --bg-tertiary: #2d2438;
    --bg-card: #221b2e;
    --bg-glass: rgba(34, 27, 46, 0.8);
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-accent: #a78bfa;
    --border-color: #374151;
    --border-light: #4b5563;
    --border-glow: rgba(139, 92, 246, 0.3);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-glow: 0 0 40px rgba(139, 92, 246, 0.3);
    --shadow-glow-lg: 0 0 60px rgba(139, 92, 246, 0.4);
    --gradient-primary: linear-gradient(135deg, #8b5cf6, #06b6d4);
    --gradient-secondary: linear-gradient(135deg, #ec4899, #8b5cf6);
    --gradient-accent: linear-gradient(135deg, #f59e0b, #ec4899);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5cf6 100%);
    --gradient-card: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
    --gradient-glass: linear-gradient(145deg, rgba(139, 92, 246, 0.1), rgba(6, 182, 212, 0.1));
    --gradient-border: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 60% 60%, rgba(245, 158, 11, 0.08) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(139,92,246,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    z-index: -1;
    opacity: 0.3;
}

@keyframes backgroundShift {
    0%, 100% {
        background:
            radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(245, 158, 11, 0.08) 0%, transparent 50%);
    }
    50% {
        background:
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(236, 72, 153, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 60% 60%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.08) 0%, transparent 50%);
    }
}

/* Enhanced Header and Navigation */
.header {
    background: rgba(12, 10, 26, 0.9);
    backdrop-filter: blur(30px);
    border-bottom: 1px solid var(--border-glow);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-xl), 0 0 30px rgba(139, 92, 246, 0.1);
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-border);
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.navbar {
    padding: 1.25rem 0;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--text-primary);
    text-decoration: none;
    position: relative;
}

.nav-logo::before {
    content: '';
    position: absolute;
    inset: -8px;
    background: var(--gradient-primary);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-logo:hover::before {
    opacity: 0.1;
}

.nav-logo i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.3));
}

.nav-menu {
    display: flex;
    gap: 0.5rem;
    list-style: none;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    padding: 8px;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.nav-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.bar {
    width: 28px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: 0.3s;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced Hero Section */
.hero {
    padding: 8rem 0;
    text-align: center;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(139, 92, 246, 0.1) 60deg, transparent 120deg);
    animation: heroShimmer 8s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroGrid" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M 30 0 L 0 0 0 30" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/><circle cx="15" cy="15" r="1" fill="rgba(139,92,246,0.3)"/></pattern></defs><rect width="100" height="100" fill="url(%23heroGrid)"/></svg>');
    opacity: 0.4;
}

@keyframes heroShimmer {
    0%, 100% {
        background:
            radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(139, 92, 246, 0.1) 60deg, transparent 120deg);
    }
    50% {
        background:
            radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            conic-gradient(from 180deg at 50% 50%, transparent 0deg, rgba(6, 182, 212, 0.1) 60deg, transparent 120deg);
    }
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.hero-subtitle {
    font-size: 1.375rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 0 auto 2rem;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.hero-features {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    position: relative;
    z-index: 1;
    flex-wrap: wrap;
}

.feature-badge {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.feature-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.3), rgba(6, 182, 212, 0.2));
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    filter: blur(1px);
}

.floating-element::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

/* Enhanced Tools Section */
.tools-section {
    padding: 8rem 0;
    background: transparent;
    position: relative;
}

.tools-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(6, 182, 212, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.08) 0%, transparent 60%);
    pointer-events: none;
    animation: sectionGlow 12s ease-in-out infinite;
}

@keyframes sectionGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.tools-grid {
    display: grid;
    grid-template-columns: 1fr 480px 1fr;
    gap: 4rem;
    align-items: start;
    position: relative;
    z-index: 1;
}

.tools-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 1;
}

.tools-header h2 {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    position: relative;
}

.tools-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.tools-header p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.tool-card {
    background: var(--bg-glass);
    border: 1px solid var(--border-glow);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 24px 24px 0 0;
}

.tool-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle, rgba(6, 182, 212, 0.1) 30%, transparent 70%);
    opacity: 0;
    transition: all 0.5s ease;
    pointer-events: none;
    animation: cardShimmer 6s ease-in-out infinite;
}

.tool-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-lg), 0 0 80px rgba(139, 92, 246, 0.3);
    border-color: var(--primary-color);
}

.tool-card:hover::after {
    opacity: 1;
}

@keyframes cardShimmer {
    0%, 100% {
        background:
            radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle, rgba(6, 182, 212, 0.1) 30%, transparent 70%);
    }
    50% {
        background:
            radial-gradient(circle, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
            radial-gradient(circle, rgba(139, 92, 246, 0.1) 30%, transparent 70%);
    }
}

/* Special styling for center card (options) */
.tool-card.options-card {
    background: linear-gradient(145deg, var(--bg-glass), rgba(139, 92, 246, 0.1));
    border: 2px solid var(--border-glow);
    transform: scale(1.05);
}

.tool-card.options-card::before {
    background: var(--gradient-secondary);
    height: 6px;
}

.tool-card.options-card:hover {
    transform: translateY(-12px) scale(1.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.card-header h3 {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-header i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.25rem;
}

/* Enhanced Input/Output Areas */
textarea {
    width: 100%;
    height: 500px;
    background: linear-gradient(145deg, var(--bg-primary), var(--bg-secondary));
    border: 2px solid var(--border-glow);
    border-radius: 16px;
    padding: 2rem;
    color: var(--text-primary);
    font-family: 'Fira Code', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.7;
    resize: vertical;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        inset 0 4px 8px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(139, 92, 246, 0.1);
    position: relative;
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow:
        inset 0 4px 8px rgba(0, 0, 0, 0.2),
        0 0 0 4px rgba(139, 92, 246, 0.2),
        0 0 40px rgba(139, 92, 246, 0.3),
        var(--shadow-glow);
    transform: scale(1.01);
}

textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
    opacity: 0.7;
}

/* Code syntax highlighting effect */
textarea:not(:placeholder-shown) {
    background:
        linear-gradient(145deg, var(--bg-primary), var(--bg-secondary)),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 4ch,
            rgba(139, 92, 246, 0.05) 4ch,
            rgba(139, 92, 246, 0.05) 4.2ch
        );
}

/* Enhanced Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-xl), var(--shadow-glow-lg), 0 0 40px rgba(139, 92, 246, 0.6);
}

.btn-primary:hover::after {
    transform: translateX(100%);
}

.btn-secondary {
    background: linear-gradient(145deg, var(--bg-tertiary), var(--bg-secondary));
    color: var(--text-secondary);
    border: 1px solid var(--border-glow);
    backdrop-filter: blur(15px);
    box-shadow: var(--shadow-md), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    background: linear-gradient(145deg, var(--border-light), var(--bg-tertiary));
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(139, 92, 246, 0.2);
    border-color: var(--primary-color);
}

.input-actions,
.output-actions {
    display: flex;
    gap: 0.75rem;
}

.process-btn {
    width: 100%;
    justify-content: center;
    margin-top: 2.5rem;
    padding: 1.5rem;
    font-size: 1.25rem;
    font-weight: 800;
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-radius: 16px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    overflow: hidden;
}

.process-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.8s ease;
}

.process-btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow-lg), 0 0 50px rgba(236, 72, 153, 0.6);
}

.process-btn:hover::before {
    transform: translateX(100%);
}

.process-btn:active {
    transform: translateY(-2px) scale(1.01);
}

/* Enhanced Options */
.options-grid {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.option-group {
    background: linear-gradient(145deg, var(--bg-primary), var(--bg-secondary));
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--border-glow);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.option-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 16px 16px 0 0;
}

.option-group::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.option-group:hover::after {
    opacity: 1;
}

.option-group h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.option-group h4::before {
    content: '';
    width: 6px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 3px;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    margin-bottom: 1rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.checkbox-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(6, 182, 212, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.checkbox-label:hover {
    color: var(--text-primary);
    border-color: var(--border-glow);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.checkbox-label:hover::before {
    opacity: 1;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-glow);
    border-radius: 8px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--bg-secondary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(139, 92, 246, 0.5);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.js-var-options {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.js-var-options input,
.js-var-options select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.js-var-options input:focus,
.js-var-options select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Output Stats */
.output-stats {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(145deg, var(--bg-primary), var(--bg-secondary));
    border-radius: 16px;
    font-size: 0.875rem;
    color: var(--text-muted);
    display: none;
    border: 1px solid var(--border-glow);
    box-shadow: var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.output-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 16px 16px 0 0;
}

.output-stats::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
    opacity: 0.5;
    animation: statsGlow 4s ease-in-out infinite;
}

.output-stats.show {
    display: block;
    animation: slideInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes statsGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-top: 1px solid var(--border-glow);
    padding: 5rem 0 2rem;
    margin-top: 8rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 50% 10%, rgba(236, 72, 153, 0.1) 0%, transparent 60%);
    pointer-events: none;
    animation: footerGlow 10s ease-in-out infinite;
}

.footer::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-border);
    animation: borderFlow 6s linear infinite;
}

@keyframes footerGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes borderFlow {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-section p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.app-icons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
}

.app-icons a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1.5rem;
    background: var(--bg-glass);
    border: 1px solid var(--border-glow);
    border-radius: 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.app-icons a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: all 0.4s ease;
    transform: scale(0.8);
}

.app-icons a::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
}

.app-icons a:hover::before {
    opacity: 0.15;
    transform: scale(1);
}

.app-icons a:hover::after {
    width: 100px;
    height: 100px;
}

.app-icons a:hover {
    color: var(--text-primary);
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--primary-color);
}

.app-icons a i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.app-icons a span {
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    margin-top: 0.25rem;
}

.app-icons a:hover i {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nav-container {
        padding: 0 1.5rem;
    }
    
    .container {
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 90px;
        flex-direction: column;
        background: rgba(12, 10, 26, 0.95);
        width: 100%;
        text-align: center;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: var(--shadow-2xl), var(--shadow-glow);
        border-top: 1px solid var(--border-glow);
        padding: 3rem 0;
        backdrop-filter: blur(30px);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero {
        padding: 6rem 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-features {
        gap: 0.5rem;
    }

    .feature-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .floating-element {
        width: 40px;
        height: 40px;
    }

    .tools-header h2 {
        font-size: 2.5rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .tool-card.options-card {
        transform: none;
    }

    .tool-card.options-card:hover {
        transform: translateY(-8px) scale(1.02);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .app-icons {
        grid-template-columns: repeat(3, 1fr);
        justify-content: center;
        gap: 1rem;
    }

    .tool-card {
        padding: 2rem;
    }

    textarea {
        height: 400px;
        padding: 1.5rem;
    }

    .option-group {
        padding: 1.5rem;
    }

    .checkbox-label {
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .tool-card {
        padding: 1.25rem;
    }

    .app-icons {
        grid-template-columns: repeat(2, 1fr);
    }

    .nav-container {
        padding: 0 1rem;
    }

    .container {
        padding: 0 1rem;
    }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.tool-card {
    animation: fadeInUp 0.8s ease-out;
}

.tool-card:nth-child(2) {
    animation-delay: 0.2s;
}

.tool-card:nth-child(3) {
    animation-delay: 0.4s;
}

/* Loading States */
.processing {
    opacity: 0.7;
    pointer-events: none;
}

.processing .process-btn {
    background: var(--bg-tertiary);
    cursor: not-allowed;
    animation: pulse 1.5s infinite;
}

.processing .process-btn::after {
    content: '';
    width: 18px;
    height: 18px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 6px;
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* Loading Animation */
body.loading {
    overflow: hidden;
}

body.loading::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeOut 1s ease-out 2s forwards;
}

body.loading::after {
    content: '';
    position: fixed;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    border: 4px solid transparent;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    z-index: 10001;
    animation: spin 1s linear infinite, fadeOut 1s ease-out 2s forwards;
    transform: translate(-50%, -50%);
}

@keyframes fadeOut {
    to {
        opacity: 0;
        visibility: hidden;
    }
}

/* Page Load Animation */
.loaded .tool-card {
    animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.loaded .tool-card:nth-child(1) {
    animation-delay: 0.2s;
}

.loaded .tool-card:nth-child(2) {
    animation-delay: 0.4s;
}

.loaded .tool-card:nth-child(3) {
    animation-delay: 0.6s;
}

/* Particle Effect */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: particleFloat 20s linear infinite;
    opacity: 0.3;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}
