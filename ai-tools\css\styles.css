/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #2a2f3e;
    --bg-card: #1e2332;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #374151;
    --border-light: #4b5563;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-card: linear-gradient(145deg, var(--bg-card), var(--bg-tertiary));
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* Enhanced Header and Navigation */
.header {
    background: rgba(10, 14, 26, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.navbar {
    padding: 1.25rem 0;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--text-primary);
    text-decoration: none;
    position: relative;
}

.nav-logo::before {
    content: '';
    position: absolute;
    inset: -8px;
    background: var(--gradient-primary);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-logo:hover::before {
    opacity: 0.1;
}

.nav-logo i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.3));
}

.nav-menu {
    display: flex;
    gap: 0.5rem;
    list-style: none;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
    padding: 8px;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.nav-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.bar {
    width: 28px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: 0.3s;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced Hero Section */
.hero {
    padding: 6rem 0;
    text-align: center;
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.hero-subtitle {
    font-size: 1.375rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 0 auto 2rem;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

/* Enhanced Tools Section */
.tools-section {
    padding: 6rem 0;
    background: var(--bg-primary);
    position: relative;
}

.tools-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 40%);
    pointer-events: none;
}

.tools-grid {
    display: grid;
    grid-template-columns: 1fr 420px 1fr;
    gap: 3rem;
    align-items: start;
    position: relative;
    z-index: 1;
}

.tool-card {
    background: var(--gradient-card);
    border: 1px solid var(--border-light);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-2xl);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 20px 20px 0 0;
}

.tool-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.tool-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.2);
    border-color: var(--primary-color);
}

.tool-card:hover::after {
    opacity: 1;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.card-header h3 {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-header i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.25rem;
}

/* Enhanced Input/Output Areas */
textarea {
    width: 100%;
    height: 450px;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    color: var(--text-primary);
    font-family: 'Fira Code', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 0 0 4px rgba(99, 102, 241, 0.1),
        0 0 20px rgba(99, 102, 241, 0.2);
}

textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

/* Enhanced Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), 0 0 20px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--border-light);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.input-actions,
.output-actions {
    display: flex;
    gap: 0.75rem;
}

.process-btn {
    width: 100%;
    justify-content: center;
    margin-top: 2rem;
    padding: 1.25rem;
    font-size: 1.125rem;
    font-weight: 700;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-xl);
}

.process-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-2xl), 0 0 30px rgba(99, 102, 241, 0.5);
}

/* Enhanced Options */
.options-grid {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.option-group {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.option-group h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.option-group h4::before {
    content: '';
    width: 4px;
    height: 16px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 8px;
}

.checkbox-label:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-radius: 6px;
    position: relative;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    transform: scale(1.1);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.js-var-options {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.js-var-options input,
.js-var-options select {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.js-var-options input:focus,
.js-var-options select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Output Stats */
.output-stats {
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    font-size: 0.875rem;
    color: var(--text-muted);
    display: none;
    border: 1px solid var(--border-color);
}

.output-stats.show {
    display: block;
    animation: slideInUp 0.3s ease-out;
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-top: 1px solid var(--border-color);
    padding: 4rem 0 2rem;
    margin-top: 6rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.footer-section p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.app-icons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
}

.app-icons a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background: var(--gradient-card);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.app-icons a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.app-icons a:hover::before {
    opacity: 0.1;
}

.app-icons a:hover {
    color: var(--text-primary);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.app-icons a i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.app-icons a span {
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    margin-top: 0.25rem;
}

.app-icons a:hover i {
    transform: scale(1.2);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nav-container {
        padding: 0 1.5rem;
    }
    
    .container {
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(26, 31, 46, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-2xl);
        border-top: 1px solid var(--border-color);
        padding: 2rem 0;
        backdrop-filter: blur(20px);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .app-icons {
        grid-template-columns: repeat(3, 1fr);
        justify-content: center;
    }

    .tool-card {
        padding: 1.5rem;
    }

    textarea {
        height: 350px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .tool-card {
        padding: 1.25rem;
    }

    .app-icons {
        grid-template-columns: repeat(2, 1fr);
    }

    .nav-container {
        padding: 0 1rem;
    }

    .container {
        padding: 0 1rem;
    }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.tool-card {
    animation: fadeInUp 0.8s ease-out;
}

.tool-card:nth-child(2) {
    animation-delay: 0.2s;
}

.tool-card:nth-child(3) {
    animation-delay: 0.4s;
}

/* Loading States */
.processing {
    opacity: 0.7;
    pointer-events: none;
}

.processing .process-btn {
    background: var(--bg-tertiary);
    cursor: not-allowed;
    animation: pulse 1.5s infinite;
}

.processing .process-btn::after {
    content: '';
    width: 18px;
    height: 18px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-light);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
